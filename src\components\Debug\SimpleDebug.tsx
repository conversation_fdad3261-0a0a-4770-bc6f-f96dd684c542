'use client';

import { useState } from 'react';
import { userService } from '@/services/userService';
import { linkService } from '@/services/linkService';

export default function SimpleDebug() {
  const [results, setResults] = useState<any>(null);
  const [loading, setLoading] = useState(false);

  const testDatabase = async () => {
    setLoading(true);
    try {
      console.log('🔍 Testing database connection...');
      
      // Test 1: Get all users
      const users = await userService.getAll();
      console.log('👥 Users found:', users);
      
      // Test 2: Find users by different usernames
      const matiUser1 = await userService.getByUsername('matura.bock');
      const matiUser2 = await userService.getByUsername('mati');
      const pasiUser = await userService.getByUsername('pasi91');
      const gabiUser = await userService.getByUsername('Gabi23');
      
      console.log('👤 Mati user (matura.bock):', matiUser1);
      console.log('👤 Mati user (mati):', matiUser2);
      console.log('👤 Pasi user:', pasiUser);
      console.log('👤 Gabi user:', gabiUser);
      
      // Test 3: Get links for all found users
      const currentMati = matiUser1 || matiUser2; // Use whichever exists
      
      let matiLinks: any[] = [];
      let pasiLinks: any[] = [];
      let gabiLinks: any[] = [];
      let allLinksResponse: any = null;
      let pasiLinksManual: any[] = [];
      
      if (currentMati) {
        matiLinks = await linkService.getByUserId(currentMati.id, true); // include unapproved
        console.log('🔗 Mati links:', matiLinks);
      }
      
      if (pasiUser) {
        pasiLinks = await linkService.getByUserId(pasiUser.id, true);
        console.log('🔗 Pasi links:', pasiLinks);
        
        // Extra debug: Check all links in database and filter manually
        allLinksResponse = await linkService.getAll({ pageSize: 200 });
        const allLinks = allLinksResponse.data;
        pasiLinksManual = allLinks.filter((link: any) => 
          link.submittedBy === pasiUser.id || 
          link.submitter?.id === pasiUser.id ||
          link.submitter?.username === pasiUser.username
        );
        console.log('🔍 ALL links in database:', allLinks.length);
        console.log('🔍 Pasi links found manually:', pasiLinksManual);
        console.log('🔍 Pasi user ID we are searching for:', pasiUser.id);
        
        // Show detailed link info
        console.log('🔍 ALL LINKS WITH SUBMITTER INFO:');
        allLinks.forEach((link: any, index: number) => {
          console.log(`Link ${index + 1}:`, {
            id: link.id,
            title: link.title,
            submittedBy: link.submittedBy,
            submitterUsername: link.submitter?.username,
            submitterDisplayName: link.submitter?.displayName,
            isApproved: link.isApproved
          });
        });
      }
      
      if (gabiUser) {
        gabiLinks = await linkService.getByUserId(gabiUser.id, true);
        console.log('🔗 Gabi links:', gabiLinks);
      }
      
      setResults({
        totalUsers: users.length,
        uniqueUsers: [...new Set(users.map(u => u.username))].length, // Count unique usernames
        userList: users.map(u => ({ username: u.username, displayName: u.displayName })),
        foundUsers: {
          matiUser1,
          matiUser2, 
          pasiUser,
          gabiUser
        },
        linkCounts: {
          matiLinks: matiLinks.length,
          pasiLinks: pasiLinks.length,
          gabiLinks: gabiLinks.length
        },
        allLinks: {
          mati: matiLinks.map(l => ({ title: l.title, isApproved: l.isApproved, id: l.id, submittedBy: l.submittedBy })),
          pasi: pasiLinks.map(l => ({ title: l.title, isApproved: l.isApproved, id: l.id, submittedBy: l.submittedBy })),
          gabi: gabiLinks.map(l => ({ title: l.title, isApproved: l.isApproved, id: l.id, submittedBy: l.submittedBy }))
        },
        debugInfo: {
          pasiUserId: pasiUser?.id,
          totalLinksInDb: allLinksResponse?.data?.length || 0,
          pasiLinksFromQuery: pasiLinks.length,
          pasiLinksManualFilter: pasiLinksManual?.length || 0,
          allLinksWithSubmitters: allLinksResponse?.data?.map((link: any) => ({
            id: link.id,
            title: link.title,
            submittedBy: link.submittedBy,
            submitterUsername: link.submitter?.username,
            isApproved: link.isApproved
          })) || [],
          pasiLinksFoundManually: pasiLinksManual?.map((link: any) => ({
            id: link.id,
            title: link.title,
            submittedBy: link.submittedBy,
            submitterUsername: link.submitter?.username,
            isApproved: link.isApproved
          })) || []
        }
      });
      
    } catch (error) {
      console.error('🚨 Database test error:', error);
      setResults({ error: error instanceof Error ? error.message : 'Unknown error' });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="p-4 bg-gray-800 text-white rounded-lg mb-4">
      <h3 className="text-lg font-bold mb-4">🧪 Simple Database Test</h3>
      
      <button
        onClick={testDatabase}
        disabled={loading}
        className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 disabled:opacity-50"
      >
        {loading ? '🔄 Testing...' : '🚀 Test Database'}
      </button>

      {results && (
        <div className="mt-4">
          <h4 className="font-semibold mb-2">Results:</h4>
          <pre className="bg-black p-3 rounded text-green-400 text-sm overflow-auto">
            {JSON.stringify(results, null, 2)}
          </pre>
        </div>
      )}
    </div>
  );
} 