'use client';

import { useState } from 'react';
import { followService } from '@/services/followService';
import { userService } from '@/services/userService';

export default function FollowTestTool() {
  const [results, setResults] = useState<any>(null);
  const [loading, setLoading] = useState(false);

  const testAndCreateFollows = async () => {
    setLoading(true);
    try {
      console.log('🔗 Testing follow relationships...');
      
      // Get users
      const matiUser = await userService.getByUsername('mati');
      const pasiUser = await userService.getByUsername('pasi91');
      const gabiUser = await userService.getByUsername('Gabi23');
      
      if (!matiUser || !pasiUser) {
        setResults({ error: 'Users not found', matiUser, pasiUser, gabiUser });
        return;
      }
      
      // Test current follow status
      const matiFollowsPasi = await followService.isFollowing(matiUser.id, pasiUser.id);
      const gabiFollowsPasi = gabiUser ? await followService.isFollowing(gabiUser.id, pasiUser.id) : false;
      
      console.log('👥 Current follow status:', { matiFollowsPasi, gabiFollowsPasi });
      
      // Create some test follows if they don't exist
      if (!matiFollowsPasi) {
        console.log('➕ Creating follow: mati -> pasi');
        await followService.follow(matiUser.id, pasiUser.id);
      }
      
      if (gabiUser && !gabiFollowsPasi) {
        console.log('➕ Creating follow: gabi -> pasi');
        await followService.follow(gabiUser.id, pasiUser.id);
      }
      
      // Re-check follow status
      const newMatiFollowsPasi = await followService.isFollowing(matiUser.id, pasiUser.id);
      const newGabiFollowsPasi = gabiUser ? await followService.isFollowing(gabiUser.id, pasiUser.id) : false;
      
      // Get follow lists
      const matiFollowing = await followService.getFollowing(matiUser.id);
      const pasiFollowers = await followService.getFollowers(pasiUser.id);
      
      setResults({
        users: { matiUser, pasiUser, gabiUser },
        beforeFollow: { matiFollowsPasi, gabiFollowsPasi },
        afterFollow: { matiFollowsPasi: newMatiFollowsPasi, gabiFollowsPasi: newGabiFollowsPasi },
        followLists: {
          matiFollowing,
          pasiFollowers
        },
        success: true
      });
      
    } catch (error) {
      console.error('🚨 Follow test error:', error);
      setResults({ error: error instanceof Error ? error.message : 'Unknown error' });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="p-4 bg-purple-900 text-white rounded-lg mb-4">
      <h3 className="text-lg font-bold mb-4">🔗 Follow Relationship Test</h3>
      
      <button
        onClick={testAndCreateFollows}
        disabled={loading}
        className="bg-purple-600 text-white px-4 py-2 rounded hover:bg-purple-700 disabled:opacity-50"
      >
        {loading ? '🔄 Testing...' : '🚀 Test & Create Follows'}
      </button>

      {results && (
        <div className="mt-4">
          <h4 className="font-semibold mb-2">Results:</h4>
          <pre className="bg-black p-3 rounded text-green-400 text-sm overflow-auto max-h-80">
            {JSON.stringify(results, null, 2)}
          </pre>
        </div>
      )}
    </div>
  );
} 