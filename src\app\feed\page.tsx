'use client';

import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/context/AuthContext';
import { linkService } from '@/services/linkService';
import { feedService } from '@/firebase/services/feedService';
import { LinkWithDetails } from '@/types';
import LinkCard from '@/components/UI/LinkCard';

import {
  HeartIcon,
  UserGroupIcon,
  SparklesIcon,
  MagnifyingGlassIcon
} from '@heroicons/react/24/outline';

export default function FeedPage() {
  const router = useRouter();
  const { user } = useAuth();
  const [feedLinks, setFeedLinks] = useState<LinkWithDetails[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'following' | 'discover'>('following');

  useEffect(() => {
    if (!user) {
      router.push('/login');
      return;
    }

    loadFeed();
  }, [user, router, activeTab]);

  const loadFeed = async () => {
    if (!user) return;

    setLoading(true);
    setError(null);

    try {
      let links: LinkWithDetails[] = [];
      
      if (activeTab === 'following') {
        // Use the new feedService for personalized feed
        const feedResponse = await feedService.getPersonalizedFeed({
          userId: user.id,
          pageSize: 20,
          timeRange: 'week',
          includeOwnLinks: false
        });
        links = feedResponse.data;
      } else {
        // Use the new feedService for discovery
        const discoveryResponse = await feedService.getDiscoveryFeed({
          userId: user.id,
          pageSize: 20,
          timeRange: 'week',
          sortBy: 'popular'
        });
        links = discoveryResponse.data;
      }

      setFeedLinks(links);
    } catch (err: any) {
      console.error('Error loading feed:', err);
      setError('Fehler beim Laden des Feeds');
    } finally {
      setLoading(false);
    }
  };

  if (!user) {
    return null; // Will redirect to login
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Dein persönlicher Feed
          </h1>
          <p className="text-gray-600">
            Entdecke neue Links von Personen, denen du folgst, oder erkunde beliebte Inhalte
          </p>
        </div>



        {/* Tab Navigation */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
          <div className="flex">
            <button
              onClick={() => setActiveTab('following')}
              className={`flex-1 flex items-center justify-center px-6 py-4 text-sm font-medium border-b-2 transition-colors ${
                activeTab === 'following'
                  ? 'border-blue-600 text-blue-600 bg-blue-50'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:bg-gray-50'
              }`}
            >
              <UserGroupIcon className="h-5 w-5 mr-2" />
              Folge ich ({feedLinks.length})
            </button>
            <button
              onClick={() => setActiveTab('discover')}
              className={`flex-1 flex items-center justify-center px-6 py-4 text-sm font-medium border-b-2 transition-colors ${
                activeTab === 'discover'
                  ? 'border-blue-600 text-blue-600 bg-blue-50'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:bg-gray-50'
              }`}
            >
              <SparklesIcon className="h-5 w-5 mr-2" />
              Entdecken
            </button>
          </div>
        </div>

        {/* Content */}
        {loading ? (
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">Feed wird geladen...</p>
          </div>
        ) : error ? (
          <div className="text-center py-12">
            <div className="text-red-600 mb-4">
              <SparklesIcon className="h-12 w-12 mx-auto" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">Fehler beim Laden</h3>
            <p className="text-gray-600 mb-4">{error}</p>
            <button
              onClick={loadFeed}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 transition-colors"
            >
              Erneut versuchen
            </button>
          </div>
        ) : feedLinks.length > 0 ? (
          <div className="space-y-6">
            {feedLinks.map((link) => (
              <div key={link.id} className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <LinkCard link={link} />
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            {activeTab === 'following' ? (
              <>
                <UserGroupIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Noch keine Links in deinem Feed
                </h3>
                <p className="text-gray-600 mb-6">
                  Folge anderen Benutzern, um ihre geteilten Links in deinem Feed zu sehen.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <button
                    onClick={() => setActiveTab('discover')}
                    className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 transition-colors"
                  >
                    <SparklesIcon className="h-4 w-4 mr-2" />
                    Entdecken
                  </button>
                  <button
                    onClick={() => router.push('/links')}
                    className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors"
                  >
                    <MagnifyingGlassIcon className="h-4 w-4 mr-2" />
                    Alle Links durchsuchen
                  </button>
                </div>
              </>
            ) : (
              <>
                <SparklesIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Keine beliebten Links gefunden
                </h3>
                <p className="text-gray-600 mb-6">
                  Momentan gibt es keine beliebten Links zu entdecken.
                </p>
                <button
                  onClick={() => router.push('/links')}
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 transition-colors"
                >
                  <MagnifyingGlassIcon className="h-4 w-4 mr-2" />
                  Alle Links durchsuchen
                </button>
              </>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
