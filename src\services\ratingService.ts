import {
  collection,
  doc,
  addDoc,
  updateDoc,
  deleteDoc,
  getDoc,
  getDocs,
  query,
  where,
  serverTimestamp,
  writeBatch
} from 'firebase/firestore';
import { db } from '@/firebase/config';
import { Rating } from '@/types';
import { COLLECTIONS } from '@/firebase/types/collections';
import { serializeDoc } from '@/firebase/utils/firestore-helpers';

export const ratingService = {
  async create(ratingData: Omit<Rating, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
    console.log('⭐ RatingService.create called with data:', ratingData);
    
    try {
      // Check if user already rated this link
      const existingRating = await this.getByUserAndLink(ratingData.userId, ratingData.linkId);
      if (existingRating) {
        throw new Error('User has already rated this link');
      }

      const docRef = await addDoc(collection(db, COLLECTIONS.RATINGS), {
        ...ratingData,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      });

      // Update link's rating statistics
      await this.updateLinkRatingStats(ratingData.linkId);

      console.log('✅ Rating created successfully with ID:', docRef.id);
      return docRef.id;
    } catch (error) {
      console.error('🚨 Error creating rating:', error);
      throw error;
    }
  },

  async update(id: string, data: Partial<Rating>): Promise<void> {
    console.log('⭐ RatingService.update called:', { id, data });
    
    try {
      // Get current rating to know which link to update
      const currentRating = await this.getById(id);
      if (!currentRating) {
        throw new Error('Rating not found');
      }

      await updateDoc(doc(db, COLLECTIONS.RATINGS, id), {
        ...data,
        updatedAt: serverTimestamp()
      });

      // Update link's rating statistics
      await this.updateLinkRatingStats(currentRating.linkId);

      console.log('✅ Rating updated successfully');
    } catch (error) {
      console.error('🚨 Error updating rating:', error);
      throw error;
    }
  },

  async delete(id: string): Promise<void> {
    console.log('⭐ RatingService.delete called with id:', id);
    
    try {
      // Get rating data first to update link stats
      const rating = await this.getById(id);
      if (!rating) {
        throw new Error('Rating not found');
      }

      await deleteDoc(doc(db, COLLECTIONS.RATINGS, id));

      // Update link's rating statistics
      await this.updateLinkRatingStats(rating.linkId);

      console.log('✅ Rating deleted successfully');
    } catch (error) {
      console.error('🚨 Error deleting rating:', error);
      throw error;
    }
  },

  async getById(id: string): Promise<Rating | null> {
    console.log('⭐ RatingService.getById called with id:', id);
    
    try {
      const docSnap = await getDoc(doc(db, COLLECTIONS.RATINGS, id));
      if (docSnap.exists()) {
        const ratingData = serializeDoc<Rating>(docSnap);
        console.log('⭐ Rating data loaded:', ratingData);
        return ratingData;
      } else {
        console.log('⭐ No rating found with id:', id);
        return null;
      }
    } catch (error) {
      console.error('🚨 Error in ratingService.getById:', error);
      return null;
    }
  },

  async getByUserAndLink(userId: string, linkId: string): Promise<Rating | null> {
    console.log('⭐ RatingService.getByUserAndLink called:', { userId, linkId });
    
    try {
      const q = query(
        collection(db, COLLECTIONS.RATINGS),
        where('userId', '==', userId),
        where('linkId', '==', linkId)
      );
      const querySnapshot = await getDocs(q);
      
      if (querySnapshot.empty) {
        console.log('⭐ No rating found for user and link');
        return null;
      }
      
      const ratingData = serializeDoc<Rating>(querySnapshot.docs[0]);
      console.log('⭐ Rating found:', ratingData);
      return ratingData;
    } catch (error) {
      console.error('🚨 Error in ratingService.getByUserAndLink:', error);
      return null;
    }
  },

  async getByLink(linkId: string): Promise<Rating[]> {
    console.log('⭐ RatingService.getByLink called with linkId:', linkId);
    
    try {
      const q = query(
        collection(db, COLLECTIONS.RATINGS),
        where('linkId', '==', linkId)
      );
      const querySnapshot = await getDocs(q);
      const ratings = querySnapshot.docs.map(doc => serializeDoc<Rating>(doc));
      
      console.log('⭐ Found', ratings.length, 'ratings for link');
      return ratings;
    } catch (error) {
      console.error('🚨 Error getting link ratings:', error);
      return [];
    }
  },

  async updateLinkRatingStats(linkId: string): Promise<void> {
    console.log('⭐ RatingService.updateLinkRatingStats called for linkId:', linkId);
    
    try {
      const ratings = await this.getByLink(linkId);
      const totalRatings = ratings.length;
      const averageRating = totalRatings > 0 
        ? ratings.reduce((sum, rating) => sum + rating.rating, 0) / totalRatings 
        : 0;

      const linkRef = doc(db, COLLECTIONS.LINKS, linkId);
      await updateDoc(linkRef, {
        totalRatings,
        averageRating: Math.round(averageRating * 10) / 10 // Round to 1 decimal place
      });

      console.log('✅ Link rating stats updated:', { totalRatings, averageRating });
    } catch (error) {
      console.error('🚨 Error updating link rating stats:', error);
    }
  }
};
