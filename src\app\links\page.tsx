'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';

import { FilterOptions } from '@/types';
import LinkCard from '@/components/UI/LinkCard';
import {
  FunnelIcon,
  MagnifyingGlassIcon,
  PlusIcon,
  ArrowPathIcon
} from '@heroicons/react/24/outline';
import { useAuth } from '@/context/AuthContext';
import { LinkWithDetails } from '@/types';
import { useLinks } from '@/hooks/useLinks';
import { useSearch } from '@/hooks/useSearch';

export default function LinksPage() {
  const { user } = useAuth();
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState('');
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState({
    sortBy: 'newest',
    timeRange: 'all',
    domain: undefined
  });

  const {
    links,
    loading: linksLoading,
    error: linksError,
    hasMore,
    loadMore: fetchNextPage
  } = useLinks({});

  const {
    results: searchResults,
    loading: searchLoading,
    error: searchError,
    search
  } = useSearch();

  const isSearchMode = searchQuery.trim().length > 0;
  const displayLinks = isSearchMode ? searchResults || [] : links || [];
  const displayLoading = isSearchMode ? searchLoading : linksLoading;
  const displayError = isSearchMode ? searchError : linksError;
  const displayHasMore = !isSearchMode && hasMore;
  const displayLoadMore = () => {
    if (!isSearchMode) {
      fetchNextPage();
    }
  };

  const handleFilterChange = (newFilters: Partial<FilterOptions>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  };

  const handleSearch = () => {
    if (searchQuery.trim()) {
      search(searchQuery);
    }
  };

  const handleClearSearch = () => {
    setSearchQuery('');
  };

  const handleAdvancedSearch = () => {
    router.push(`/search?q=${encodeURIComponent(searchQuery)}`);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Alle Links</h1>
              <p className="mt-2 text-gray-600">
                Entdecke die neuesten und besten Links der Community
              </p>
            </div>
            {user && (
              <div className="mt-4 sm:mt-0">
                <Link
                  href="/submit"
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 transition-colors"
                >
                  <PlusIcon className="h-4 w-4 mr-2" />
                  Link einreichen
                </Link>
              </div>
            )}
          </div>
        </div>

        {/* Search and Filters */}
        <div className="mb-6 space-y-4">
          {/* Search Bar */}
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="text"
              placeholder="Links durchsuchen..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  handleSearch();
                }
              }}
              className="block w-full pl-10 pr-32 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
            />
            <div className="absolute inset-y-0 right-0 flex items-center space-x-2 pr-3">
              {searchQuery && (
                <button
                  onClick={handleClearSearch}
                  className="text-gray-400 hover:text-gray-600"
                >
                  ×
                </button>
              )}
              <button
                onClick={handleSearch}
                disabled={!searchQuery.trim() || displayLoading}
                className="px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700 disabled:opacity-50"
              >
                {displayLoading && isSearchMode ? (
                  <ArrowPathIcon className="h-3 w-3 animate-spin" />
                ) : (
                  'Suchen'
                )}
              </button>
              {searchQuery && (
                <button
                  onClick={handleAdvancedSearch}
                  className="px-3 py-1 border border-gray-300 text-gray-700 text-sm rounded hover:bg-gray-50"
                >
                  Erweitert
                </button>
              )}
            </div>
          </div>

          {/* Filter Toggle */}
          <div className="flex items-center justify-between">
            <button
              onClick={() => setShowFilters(!showFilters)}
              className="inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors"
            >
              <FunnelIcon className="h-4 w-4 mr-2" />
              Filter
            </button>
            
            <div className="text-sm text-gray-500">
              {displayLinks.length} Links {isSearchMode ? 'gefunden' : 'verfügbar'}
              {isSearchMode && searchQuery && (
                <span className="ml-2 text-blue-600">für &quot;{searchQuery}&quot;</span>
              )}
            </div>
          </div>

          {/* Filters */}
          {showFilters && (
            <div className="bg-white rounded-lg border border-gray-200 p-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {/* Sort By */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Sortieren nach
                  </label>
                  <select
                    value={filters.sortBy}
                    onChange={(e) => handleFilterChange({ sortBy: e.target.value as FilterOptions['sortBy'] })}
                    className="block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="newest">Neueste zuerst</option>
                    <option value="oldest">Älteste zuerst</option>
                    <option value="rating">Beste Bewertung</option>
                    <option value="popular">Beliebteste</option>
                  </select>
                </div>

                {/* Time Range */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Zeitraum
                  </label>
                  <select
                    value={filters.timeRange}
                    onChange={(e) => handleFilterChange({ timeRange: e.target.value as FilterOptions['timeRange'] })}
                    className="block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="all">Alle Zeit</option>
                    <option value="day">Letzter Tag</option>
                    <option value="week">Letzte Woche</option>
                    <option value="month">Letzter Monat</option>
                    <option value="year">Letztes Jahr</option>
                  </select>
                </div>

                {/* Domain Filter */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Domain
                  </label>
                  <input
                    type="text"
                    placeholder="z.B. github.com"
                    value={filters.domain || ''}
                    onChange={(e) => handleFilterChange({ domain: e.target.value || undefined })}
                    className="block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Error State */}
        {displayError && (
          <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-6">
            <div className="text-red-800">
              <strong>Fehler:</strong> {displayError}
            </div>
          </div>
        )}

        {/* Links List */}
        <div className="space-y-6">
          {displayLoading && displayLinks.length === 0 ? (
            // Loading skeleton
            [...Array(5)].map((_, i) => (
              <div key={i} className="animate-pulse">
                <div className="bg-white rounded-lg border border-gray-200 p-6">
                  <div className="h-4 bg-gray-200 rounded w-3/4 mb-4"></div>
                  <div className="h-3 bg-gray-200 rounded w-full mb-2"></div>
                  <div className="h-3 bg-gray-200 rounded w-2/3"></div>
                </div>
              </div>
            ))
          ) : displayLinks.length > 0 ? (
            <>
              {displayLinks.map((link: LinkWithDetails) => (
                <LinkCard
                  key={link.id}
                  link={link}
                  showCategory={true}
                />
              ))}

              {/* Load More Button */}
              {displayHasMore && (
                <div className="text-center pt-6">
                  <button
                    onClick={displayLoadMore}
                    disabled={displayLoading}
                    className="inline-flex items-center px-6 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors disabled:opacity-50"
                  >
                    {displayLoading ? 'Lädt...' : 'Mehr laden'}
                  </button>
                </div>
              )}
            </>
          ) : (
            // Empty state
            <div className="text-center py-12">
              <MagnifyingGlassIcon className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Keine Links gefunden
              </h3>
              <p className="text-gray-600 mb-6">
                {isSearchMode && searchQuery
                  ? `Keine Links gefunden für &quot;${searchQuery}&quot;. Versuche andere Suchbegriffe.`
                  : 'Noch keine Links vorhanden. Sei der Erste!'
                }
              </p>
              {user && (
                <Link
                  href="/submit"
                  className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 transition-colors"
                >
                  <PlusIcon className="h-5 w-5 mr-2" />
                  Link einreichen
                </Link>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
