'use client';

import { useState } from 'react';
import { followService } from '@/services/followService';
import { linkService } from '@/services/linkService';
import { feedService } from '@/firebase/services/feedService';
import { userService } from '@/services/userService';
import { User } from '@/types';

export default function FeedDebug() {
  const [debugInfo, setDebugInfo] = useState<any>(null);
  const [loading, setLoading] = useState(false);

  const debugFeed = async () => {
    setLoading(true);
    try {
      // Ermittle die echten User IDs aus Firebase
      console.log('🔍 Starting feed debug...');
      
      // Hole zuerst alle Benutzer, um die verfügbaren Usernames zu sehen
      const allUsers = await userService.getAll();
      console.log('👥 All users in system:', allUsers.map(u => ({ id: u.id, username: u.username, displayName: u.displayName })));
      
      // Versuche verschiedene mögliche Benutzernamen
      let gabiUser = await userService.getByUsername('gabi23');
      if (!gabiUser) {
        gabiUser = await userService.getByUsername('gabi');
      }
      
      let pasiUser = await userService.getByUsername('pasi91');
      if (!pasiUser) {
        pasiUser = await userService.getByUsername('pasi');
      }
      
      // Falls immer noch nicht gefunden, nutze den aktuell eingeloggten User als Basis
      const currentUserName = 'mati'; // mati ist der eingeloggte User
      let currentTestUser = await userService.getByUsername(currentUserName);
      if (!currentTestUser) {
        // Versuche verschiedene Varianten für mati
        currentTestUser = await userService.getByUsername('matura.bock');
      }
      
      if (!currentTestUser) {
        console.error('❌ Could not find any test users:', { gabiUser, pasiUser, currentTestUser });
        setDebugInfo({ 
          error: 'No test users found',
          allUsers: allUsers.map(u => ({ username: u.username, displayName: u.displayName })),
          gabiFound: !!gabiUser,
          pasiFound: !!pasiUser,
          currentUserFound: !!currentTestUser
        });
        return;
      }

      // Nutze verfügbare Benutzer oder den aktuellen User für Tests
      const testUserId = currentTestUser.id;
      const targetUserId = gabiUser?.id || pasiUser?.id || allUsers[0]?.id;
      
      console.log('📝 Found users:', { testUserId, targetUserId });

      // 1. Check follow relationship
      const isFollowing = await followService.isFollowing(testUserId, targetUserId);
      console.log('👥 Is test user following target?', isFollowing);

      // 2. Get test user's following list
      const following = await followService.getFollowing(testUserId);
      console.log('📋 Test user is following:', following);

      // 3. Get target user's links
      const targetLinks = await linkService.getByUserId(targetUserId);
      console.log('🔗 Target user\'s links:', targetLinks);

      // 4. Test feed function
      const feed = await feedService.getPersonalizedFeed({
        userId: testUserId,
        pageSize: 10,
        timeRange: 'all',
        includeOwnLinks: false
      });
      console.log('🎯 Test user\'s feed:', feed);

      setDebugInfo({
        isFollowing,
        following,
        targetLinks,
        feed,
        testUserId,
        targetUserId,
        users: { currentTestUser, gabiUser, pasiUser },
        allUsers: allUsers.map(u => ({ username: u.username, displayName: u.displayName }))
      });

    } catch (error) {
      console.error('🚨 Debug error:', error);
      setDebugInfo({ error: error instanceof Error ? error.message : 'Unknown error' });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="p-4 bg-gray-900 border border-gray-700 rounded-lg">
      <h3 className="text-lg font-semibold mb-4 text-white">🔍 Feed Debug Tool</h3>
      
      <button
        onClick={debugFeed}
        disabled={loading}
        className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 disabled:opacity-50 transition-colors"
      >
        {loading ? '🔄 Debugging...' : '🚀 Debug Feed'}
      </button>

      {debugInfo && (
        <div className="mt-4 p-4 bg-black border border-gray-600 rounded">
          <h4 className="text-white font-semibold mb-2">Debug Results:</h4>
          <pre className="text-sm overflow-auto max-h-96 text-green-400 font-mono">
            {JSON.stringify(debugInfo, null, 2)}
          </pre>
        </div>
      )}

      <div className="mt-4 text-sm text-gray-300">
        <p className="text-white font-semibold">Schritte:</p>
        <ol className="list-decimal ml-6 mt-2">
          <li>Follow-Beziehung überprüfen</li>
          <li>Gabi's Following-Liste laden</li>
          <li>Pasi's Links laden</li>
          <li>Feed generieren</li>
        </ol>
      </div>
    </div>
  );
} 