import {
  DocumentSnapshot,
  QueryDocumentSnapshot,
  DocumentData,
  Timestamp
} from 'firebase/firestore';

// Helper function to convert Firestore timestamp to Date
export const convertTimestamp = (timestamp: any): Date => {
  if (timestamp instanceof Timestamp) {
    return timestamp.toDate();
  }
  return timestamp;
};

// Helper function to serialize Firestore document
export const serializeDoc = <T>(doc: QueryDocumentSnapshot<DocumentData> | DocumentSnapshot<DocumentData>): T => {
  const data = doc.data();
  if (!data) throw new Error('Document not found');
  
  // Convert all timestamp fields to Date objects
  const serialized: any = { id: doc.id, ...data };
  Object.keys(serialized).forEach(key => {
    if (serialized[key] instanceof Timestamp) {
      serialized[key] = serialized[key].toDate();
    }
  });
  
  return serialized as T;
};

// Helper function to serialize multiple documents
export const serializeDocs = <T>(docs: QueryDocumentSnapshot<DocumentData>[]): T[] => {
  return docs.map(doc => serializeDoc<T>(doc));
}; 