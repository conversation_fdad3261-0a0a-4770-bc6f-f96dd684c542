'use client';

import React, { useState } from 'react';
import { LinkWithDetails } from '@/types';
import { linkService } from '@/services/linkService';
import {
  CheckCircleIcon,
  XCircleIcon,
  StarIcon,
  TrashIcon,
  ArrowPathIcon
} from '@heroicons/react/24/outline';

interface BulkActionsProps {
  selectedLinks: LinkWithDetails[];
  onAction: (action: string, linkIds: string[]) => void;
  onClearSelection: () => void;
}

const BulkActions: React.FC<BulkActionsProps> = ({
  selectedLinks,
  onAction,
  onClearSelection
}) => {
  const [loading, setLoading] = useState(false);
  const [actionType, setActionType] = useState<string>('');

  const handleBulkAction = async (action: 'approve' | 'reject' | 'feature' | 'unfeature') => {
    if (selectedLinks.length === 0) return;

    const confirmed = window.confirm(
      `Bist du sicher, dass du ${selectedLinks.length} Link(s) ${
        action === 'approve' ? 'genehmigen' :
        action === 'reject' ? 'ablehnen' :
        action === 'feature' ? 'als Featured markieren' :
        'Featured-Status entfernen'
      } möchtest?`
    );

    if (!confirmed) return;

    try {
      setLoading(true);
      setActionType(action);

      const linkIds = selectedLinks.map(link => link.id);

      // Execute bulk action
      await Promise.all(
        linkIds.map(async (linkId) => {
          switch (action) {
            case 'approve':
              await linkService.approve(linkId);
              break;
            case 'reject':
              await linkService.reject(linkId);
              break;
            case 'feature':
              await linkService.feature(linkId, true);
              break;
            case 'unfeature':
              await linkService.feature(linkId, false);
              break;
          }
        })
      );

      // Notify parent component
      onAction(action, linkIds);
      onClearSelection();

      // Show success message
      const actionText = {
        approve: 'genehmigt',
        reject: 'abgelehnt',
        feature: 'als Featured markiert',
        unfeature: 'Featured-Status entfernt'
      }[action];

      alert(`${selectedLinks.length} Link(s) erfolgreich ${actionText}!`);

    } catch (error) {
      console.error(`Fehler bei Bulk-Aktion ${action}:`, error);
      alert(`Fehler bei der Ausführung der Aktion. Bitte versuche es erneut.`);
    } finally {
      setLoading(false);
      setActionType('');
    }
  };

  if (selectedLinks.length === 0) {
    return null;
  }

  return (
    <div className="bg-white border border-gray-200 rounded-lg shadow-sm p-4 mb-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <span className="text-sm font-medium text-gray-900">
            {selectedLinks.length} Link(s) ausgewählt
          </span>
          
          <div className="flex items-center space-x-2">
            {/* Approve Button */}
            <button
              onClick={() => handleBulkAction('approve')}
              disabled={loading}
              className="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {loading && actionType === 'approve' ? (
                <ArrowPathIcon className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <CheckCircleIcon className="h-4 w-4 mr-2" />
              )}
              Genehmigen
            </button>

            {/* Reject Button */}
            <button
              onClick={() => handleBulkAction('reject')}
              disabled={loading}
              className="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {loading && actionType === 'reject' ? (
                <ArrowPathIcon className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <XCircleIcon className="h-4 w-4 mr-2" />
              )}
              Ablehnen
            </button>

            {/* Feature Button */}
            <button
              onClick={() => handleBulkAction('feature')}
              disabled={loading}
              className="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-yellow-600 hover:bg-yellow-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {loading && actionType === 'feature' ? (
                <ArrowPathIcon className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <StarIcon className="h-4 w-4 mr-2" />
              )}
              Featured
            </button>

            {/* Unfeature Button */}
            <button
              onClick={() => handleBulkAction('unfeature')}
              disabled={loading}
              className="inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {loading && actionType === 'unfeature' ? (
                <ArrowPathIcon className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <StarIcon className="h-4 w-4 mr-2" />
              )}
              Unfeatured
            </button>
          </div>
        </div>

        {/* Clear Selection */}
        <button
          onClick={onClearSelection}
          disabled={loading}
          className="text-sm text-gray-500 hover:text-gray-700 disabled:opacity-50"
        >
          Auswahl aufheben
        </button>
      </div>

      {/* Selected Links Preview */}
      <div className="mt-4 border-t border-gray-200 pt-4">
        <h4 className="text-sm font-medium text-gray-900 mb-2">Ausgewählte Links:</h4>
        <div className="space-y-2 max-h-32 overflow-y-auto">
          {selectedLinks.map((link) => (
            <div key={link.id} className="flex items-center justify-between text-sm">
              <div className="flex-1 min-w-0">
                <p className="text-gray-900 truncate">{link.title}</p>
                <p className="text-gray-500 truncate">{link.url}</p>
              </div>
              <div className="flex items-center space-x-2 ml-4">
                <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                  link.isApproved 
                    ? 'bg-green-100 text-green-800' 
                    : 'bg-yellow-100 text-yellow-800'
                }`}>
                  {link.isApproved ? 'Genehmigt' : 'Wartend'}
                </span>
                {link.isFeatured && (
                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                    <StarIcon className="h-3 w-3 mr-1" />
                    Featured
                  </span>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default BulkActions;
