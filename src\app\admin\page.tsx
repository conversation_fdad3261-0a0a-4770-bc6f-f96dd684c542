'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/context/AuthContext';
import { linkService } from '@/services/linkService';
import { categoryService } from '@/services/categoryService';
import { reportService } from '@/firebase/services/reportService';
import { userService } from '@/services/userService';
import { LinkWithDetails, Category, Report } from '@/types';
import AdminOverview from '@/components/Admin/AdminOverview';
import AdminLinksManagement from '@/components/Admin/AdminLinksManagement';
import AdminCategoriesManagement from '@/components/Admin/AdminCategoriesManagement';
import { isAdmin } from '@/utils/admin';

interface AdminStats {
  totalLinks: number;
  pendingLinks: number;
  totalCategories: number;
  totalUsers: number;
  pendingReports: number;
  thisWeekSubmissions: number;
}

interface RecentActivity {
  id: string;
  type: 'link_approved' | 'link_submitted' | 'category_created' | 'report_submitted';
  title: string;
  timestamp: Date;
  color: string;
}

export default function AdminPage() {
  const { user } = useAuth();
  const router = useRouter();
  
  // State
  const [activeTab, setActiveTab] = useState<'overview' | 'links' | 'categories' | 'users' | 'reports'>('overview');
  const [pendingLinks, setPendingLinks] = useState<LinkWithDetails[]>([]);
  const [allLinks, setAllLinks] = useState<LinkWithDetails[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [reports, setReports] = useState<Report[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedLinks, setSelectedLinks] = useState<string[]>([]);
  const [stats, setStats] = useState<AdminStats>({
    totalLinks: 0,
    pendingLinks: 0,
    totalCategories: 0,
    totalUsers: 0,
    pendingReports: 0,
    thisWeekSubmissions: 0
  });
  const [recentActivities, setRecentActivities] = useState<RecentActivity[]>([]);

  // Check if user is admin
  const userIsAdmin = isAdmin(user);

  // Load data
  useEffect(() => {
    if (!user) {
      router.push('/login');
      return;
    }

    if (!userIsAdmin) {
      router.push('/');
      return;
    }

    loadData();
  }, [user, userIsAdmin, router, loadData]);

  const loadData = async () => {
    try {
      setLoading(true);
      
      const [
        pendingLinksData,
        allLinksData,
        categoriesData,
        reportsData,
        totalApprovedLinks,
        totalUsers
      ] = await Promise.all([
        linkService.getPending(),
        linkService.getAll({ pageSize: 50 }).then(response => response.data),
        categoryService.getAll(),
        reportService.getPending(),
        linkService.getTotalApprovedCount(),
        userService.getTotalCount()
      ]);

      setPendingLinks(pendingLinksData);
      setAllLinks(allLinksData);
      setCategories(categoriesData);
      setReports(reportsData);

      setStats({
        totalLinks: totalApprovedLinks,
        pendingLinks: pendingLinksData.length,
        totalCategories: categoriesData.length,
        totalUsers,
        pendingReports: reportsData.length,
        thisWeekSubmissions: 0 // TODO: Calculate this
      });

      loadRecentActivities();
    } catch (error) {
      console.error('Error loading admin data:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadRecentActivities = () => {
    const activities: RecentActivity[] = [
      ...pendingLinks.slice(0, 3).map(link => ({
        id: `link-${link.id}`,
        type: 'link_submitted' as const,
        title: `Neuer Link eingereicht: ${link.title}`,
        timestamp: link.submittedAt,
        color: 'blue'
      })),
      ...allLinks.filter(link => link.isApproved && link.approvedAt).slice(0, 2).map(link => ({
        id: `approved-${link.id}`,
        type: 'link_approved' as const,
        title: `Link genehmigt: ${link.title}`,
        timestamp: link.approvedAt!,
        color: 'green'
      }))
    ].sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime()).slice(0, 5);

    setRecentActivities(activities);
  };

  // Handlers
  const handleApproveLink = async (linkId: string) => {
    try {
      await linkService.approve(linkId);
      setPendingLinks(prev => prev.filter(link => link.id !== linkId));
      setStats(prev => ({
        ...prev,
        pendingLinks: prev.pendingLinks - 1,
        totalLinks: prev.totalLinks + 1
      }));
      loadRecentActivities();
    } catch (error) {
      console.error('Error approving link:', error);
    }
  };

  const handleRejectLink = async (linkId: string) => {
    try {
      await linkService.reject(linkId);
      setPendingLinks(prev => prev.filter(link => link.id !== linkId));
      setStats(prev => ({
        ...prev,
        pendingLinks: prev.pendingLinks - 1
      }));
      loadRecentActivities();
    } catch (error) {
      console.error('Error rejecting link:', error);
    }
  };

  const handleSelectLink = (linkId: string, selected: boolean) => {
    setSelectedLinks(prev => 
      selected 
        ? [...prev, linkId]
        : prev.filter(id => id !== linkId)
    );
  };

  const handleSelectAll = (links: LinkWithDetails[], selected: boolean) => {
    const linkIds = links.map(link => link.id);
    setSelectedLinks(prev => 
      selected 
        ? [...new Set([...prev, ...linkIds])]
        : prev.filter(id => !linkIds.includes(id))
    );
  };

  const handleBulkAction = (action: string, linkIds: string[]) => {
    if (action === 'approve') {
      setPendingLinks(prev => prev.filter(link => !linkIds.includes(link.id)));
      setAllLinks(prev => prev.map(link => 
        linkIds.includes(link.id) ? { ...link, isApproved: true, approvedAt: new Date() } : link
      ));
      setStats(prev => ({
        ...prev,
        pendingLinks: prev.pendingLinks - linkIds.length,
        totalLinks: prev.totalLinks + linkIds.length
      }));
    } else if (action === 'reject') {
      setPendingLinks(prev => prev.filter(link => !linkIds.includes(link.id)));
      setAllLinks(prev => prev.filter(link => !linkIds.includes(link.id)));
      setStats(prev => ({
        ...prev,
        pendingLinks: prev.pendingLinks - linkIds.length
      }));
    } else if (action === 'feature' || action === 'unfeature') {
      const isFeatured = action === 'feature';
      setAllLinks(prev => prev.map(link => 
        linkIds.includes(link.id) ? { ...link, isFeatured } : link
      ));
      setPendingLinks(prev => prev.map(link => 
        linkIds.includes(link.id) ? { ...link, isFeatured } : link
      ));
    }

    loadRecentActivities();
  };

  const clearSelection = () => {
    setSelectedLinks([]);
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Lade Admin-Panel...</p>
        </div>
      </div>
    );
  }

  if (!user || !isAdmin) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Admin-Panel</h1>
          <p className="mt-2 text-gray-600">Verwalte Links, Kategorien und Benutzer</p>
        </div>

        {/* Tabs */}
        <div className="border-b border-gray-200 mb-8">
          <nav className="-mb-px flex space-x-8">
            {[
              { id: 'overview', name: 'Übersicht' },
              { id: 'links', name: 'Links' },
              { id: 'categories', name: 'Kategorien' },
              { id: 'users', name: 'Benutzer' },
              { id: 'reports', name: 'Meldungen' }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as 'overview' | 'links' | 'categories' | 'users' | 'reports')}
                className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                {tab.name}
              </button>
            ))}
          </nav>
        </div>

        {/* Tab Content */}
        {activeTab === 'overview' && (
          <AdminOverview
            stats={stats}
            recentActivities={recentActivities}
            onTabChange={(tab) => setActiveTab(tab as typeof activeTab)}
          />
        )}

        {activeTab === 'links' && (
          <AdminLinksManagement
            pendingLinks={pendingLinks}
            allLinks={allLinks}
            selectedLinks={selectedLinks}
            onSelectLink={handleSelectLink}
            onSelectAll={handleSelectAll}
            onBulkAction={handleBulkAction}
            onClearSelection={clearSelection}
            onApproveLink={handleApproveLink}
            onRejectLink={handleRejectLink}
          />
        )}

        {activeTab === 'categories' && (
          <AdminCategoriesManagement
            categories={categories}
            onCategoriesChange={loadData}
            userId={user.id}
          />
        )}

        {activeTab === 'users' && (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Benutzer-Verwaltung</h3>
            <p className="text-gray-600">Benutzer-Verwaltung wird in einer zukünftigen Version implementiert.</p>
          </div>
        )}

        {activeTab === 'reports' && (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Meldungen ({reports.length})</h3>
            {reports.length === 0 ? (
              <p className="text-gray-600">Keine ausstehenden Meldungen.</p>
            ) : (
              <p className="text-gray-600">Meldungs-Verwaltung wird in einer zukünftigen Version implementiert.</p>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
