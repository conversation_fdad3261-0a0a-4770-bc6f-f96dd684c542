import {
  collection,
  query,
  where,
  orderBy,
  limit,
  startAfter,
  getDocs,
  DocumentSnapshot
} from 'firebase/firestore';
import { db } from '../config';
import { LinkWithDetails, PaginatedResponse } from '@/types';
import { COLLECTIONS } from '../types/collections';
import { linkService } from './linkService';
import { followService } from './followService';
import { favoriteService } from './favoriteService';
import { ratingService } from './ratingService';

export interface FeedOptions {
  userId: string;
  pageSize?: number;
  lastDoc?: DocumentSnapshot;
  timeRange?: 'day' | 'week' | 'month' | 'all';
  includeOwnLinks?: boolean;
  sortBy?: 'newest' | 'rating' | 'popular';
}

export interface PersonalizedFeedOptions extends FeedOptions {
  includeRecommendations?: boolean;
  categories?: string[];
  domains?: string[];
}

export const feedService = {
  /**
   * Get personalized feed for a user based on their follows
   */
  async getPersonalizedFeed(options: FeedOptions): Promise<PaginatedResponse<LinkWithDetails>> {
    const {
      userId,
      pageSize = 20,
      timeRange = 'week',
      includeOwnLinks = false,
      sortBy = 'newest'
    } = options;

    console.log('🎯 FeedService.getPersonalizedFeed called:', options);

    // Get following-based feed
    const followingFeed = await linkService.getFromFollowing(userId, {
      limitCount: pageSize,
      includeOwnLinks,
      timeRange
    });

    console.log(`✅ Generated feed with ${followingFeed.length} items for user ${userId}`);
    console.log('📊 Feed details:', followingFeed.map(link => ({ id: link.id, title: link.title, submitter: link.submitter.username })));

    return {
      data: followingFeed,
      pagination: {
        page: 1,
        limit: pageSize,
        total: followingFeed.length,
        totalPages: 1,
        hasNext: followingFeed.length === pageSize,
        hasPrev: false
      }
    };
  },

  /**
   * Get content recommendations based on user behavior
   */
  async getRecommendations(userId: string, options: {
    limitCount?: number;
    timeRange?: 'day' | 'week' | 'month' | 'all';
    excludeIds?: string[];
  } = {}): Promise<LinkWithDetails[]> {
    const { limitCount = 10, timeRange = 'week', excludeIds = [] } = options;

    console.log('🤖 Getting recommendations for user:', userId);

    // For now, get highly rated content from the specified time range
    // In a production app, you'd implement more sophisticated ML-based recommendations
    
    const timeFilter = linkService.getTimeRangeFilter(timeRange);
    let q = query(
      collection(db, COLLECTIONS.LINKS),
      where('isApproved', '==', true),
      orderBy('averageRating', 'desc'),
      orderBy('totalRatings', 'desc'),
      limit(limitCount * 2) // Get more to account for filtering
    );

    if (timeFilter) {
      q = query(q, where('submittedAt', '>=', timeFilter));
    }

    const querySnapshot = await getDocs(q);
    const links = querySnapshot.docs
      .map(doc => ({ id: doc.id, ...doc.data() }))
      .filter(link => !excludeIds.includes(link.id))
      .slice(0, limitCount);

    return linkService.enrichLinksWithDetails(links as any[]);
  },

  /**
   * Get discovery feed for users not following anyone or new users
   */
  async getDiscoveryFeed(options: FeedOptions): Promise<PaginatedResponse<LinkWithDetails>> {
    const {
      userId,
      pageSize = 20,
      timeRange = 'week',
      sortBy = 'popular'
    } = options;

    console.log('🔍 FeedService.getDiscoveryFeed called:', options);

    const timeFilter = linkService.getTimeRangeFilter(timeRange);
    let q = query(
      collection(db, COLLECTIONS.LINKS),
      where('isApproved', '==', true),
      limit(pageSize)
    );

    if (timeFilter) {
      q = query(q, where('submittedAt', '>=', timeFilter));
    }

    // Apply sorting
    if (sortBy === 'rating') {
      q = query(q, orderBy('averageRating', 'desc'), orderBy('totalRatings', 'desc'));
    } else if (sortBy === 'popular') {
      q = query(q, orderBy('totalRatings', 'desc'), orderBy('totalComments', 'desc'));
    } else {
      q = query(q, orderBy('submittedAt', 'desc'));
    }

    const querySnapshot = await getDocs(q);
    const links = querySnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
    
    const enrichedLinks = await linkService.enrichLinksWithDetails(links as any[]);

    return {
      data: enrichedLinks,
      pagination: {
        page: 1,
        limit: pageSize,
        total: enrichedLinks.length,
        totalPages: 1,
        hasNext: enrichedLinks.length === pageSize,
        hasPrev: false
      }
    };
  },

  /**
   * Enrich links with user-specific data (favorites, ratings, etc.)
   */
  async enrichWithUserData(links: LinkWithDetails[], userId: string): Promise<LinkWithDetails[]> {
    const [userFavorites, userRatings] = await Promise.all([
      favoriteService.getUserFavoriteLinks(userId),
      ratingService.getUserRatings(userId)
    ]);

    const userRatingMap = new Map(
      userRatings.map(rating => [rating.linkId, rating.rating])
    );

    return links.map(link => ({
      ...link,
      isFavorited: userFavorites.includes(link.id),
      userRating: userRatingMap.get(link.id)
    }));
  },

  /**
   * Get trending links for the homepage
   */
  async getTrendingLinks(options: {
    limitCount?: number;
    timeRange?: 'day' | 'week' | 'month';
    userId?: string;
  } = {}): Promise<LinkWithDetails[]> {
    const { limitCount = 10, timeRange = 'week', userId } = options;

    console.log('📈 Getting trending links:', options);

    const timeFilter = linkService.getTimeRangeFilter(timeRange);
    let q = query(
      collection(db, COLLECTIONS.LINKS),
      where('isApproved', '==', true),
      orderBy('totalRatings', 'desc'),
      orderBy('averageRating', 'desc'),
      limit(limitCount)
    );

    if (timeFilter) {
      q = query(q, where('submittedAt', '>=', timeFilter));
    }

    const querySnapshot = await getDocs(q);
    const links = querySnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
    
    const enrichedLinks = await linkService.enrichLinksWithDetails(links as any[]);
    
    if (userId) {
      return this.enrichWithUserData(enrichedLinks, userId);
    }
    
    return enrichedLinks;
  },

  /**
   * Get feed statistics for analytics
   */
  async getFeedStats(userId: string): Promise<{
    followingCount: number;
    newLinksToday: number;
    newLinksThisWeek: number;
    recommendationsAvailable: number;
  }> {
    const [followingIds, todayLinks, weekLinks] = await Promise.all([
      followService.getFollowing(userId),
      this.getCountByTimeRange('day'),
      this.getCountByTimeRange('week')
    ]);

    return {
      followingCount: followingIds.length,
      newLinksToday: todayLinks,
      newLinksThisWeek: weekLinks,
      recommendationsAvailable: Math.max(0, 50 - followingIds.length * 2) // Simplified calculation
    };
  },

  async getCountByTimeRange(timeRange: 'day' | 'week' | 'month'): Promise<number> {
    const timeFilter = linkService.getTimeRangeFilter(timeRange);
    if (!timeFilter) return 0;

    const q = query(
      collection(db, COLLECTIONS.LINKS),
      where('isApproved', '==', true),
      where('submittedAt', '>=', timeFilter)
    );

    const querySnapshot = await getDocs(q);
    return querySnapshot.size;
  }
};
